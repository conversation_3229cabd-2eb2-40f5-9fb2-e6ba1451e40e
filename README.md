# AI 对冲基金 - 智能投资决策系统

这是一个基于人工智能的对冲基金概念验证项目，旨在探索使用大语言模型(LLM)进行智能投资决策。该系统集成了多个专业化的AI代理，模拟知名投资者的投资策略，并结合多源数据分析来做出投资决策。

**⚠️ 重要声明：本项目仅供教育和研究目的使用，不适用于实际交易或投资。**

## 🎯 系统特色

- **🤖 多代理协作**：17个专业化AI代理，模拟知名投资者策略
- **📊 多维度分析**：基本面、技术面、情绪面、估值分析
- **📰 多源数据支持**：本地新闻数据、社交媒体数据、财务数据
- **🧠 LLM智能分析**：使用大语言模型进行深度推理和分析
- **📈 完整回测系统**：历史数据回测，性能评估和可视化
- **🔄 反思学习机制**：持续改进决策质量
- **🌐 Web界面**：直观的用户界面和实时进度跟踪

<img width="1042" alt="Screenshot 2025-03-22 at 6 19 07 PM" src="https://github.com/user-attachments/assets/cbae3dcf-b571-490d-b0ad-3f0f035ac0d4" />

## 🏛️ 投资大师代理团队

### 价值投资派
- **Warren Buffett** - 奥马哈的先知，寻找具有经济护城河的优质公司
- **Charlie Munger** - 巴菲特的合伙人，运用多元思维模型
- **Ben Graham** - 价值投资之父，坚持安全边际原则
- **Michael Burry** - 《大空头》逆势投资者，寻找深度价值

### 成长投资派
- **Phil Fisher** - 成长型投资先驱，深度"小道消息"研究
- **Peter Lynch** - 实用投资者，寻找日常生活中的"十倍股"
- **Cathie Wood** - 创新投资女王，专注颠覆性技术

### 激进投资派
- **Bill Ackman** - 激进投资者，推动企业变革
- **Stanley Druckenmiller** - 宏观投资传奇，寻找不对称机会

### 估值专家
- **Aswath Damodaran** - 纽约大学估值专家，严格的DCF分析

### 专业分析师
- **基本面分析师** - 财务数据深度分析（支持LLM智能分析）
- **技术分析师** - 价格趋势和技术指标分析
- **新闻分析师** - 新闻情感和事件影响分析（支持LLM智能分析）
- **社交媒体分析师** - 社交情感和公众舆论分析（支持LLM智能分析）
- **估值分析师** - 内在价值计算和相对估值
- **风险管理器** - 投资组合风险评估和控制
- **投资组合管理器** - 最终决策制定和交易执行
- **反思分析师** - 决策质量分析和持续改进

[![Twitter Follow](https://img.shields.io/twitter/follow/virattt?style=social)](https://twitter.com/virattt)

## ⚠️ 免责声明

本项目**仅供教育和研究目的**。

- ❌ 不适用于实际交易或投资
- ❌ 不提供投资建议或保证
- ❌ 创建者对财务损失不承担责任
- ✅ 投资决策请咨询专业财务顾问
- ⚠️ 过去的表现不代表未来的结果

使用本软件即表示您同意仅将其用于学习和研究目的。

## 📚 目录
- [🎯 系统特色](#-系统特色)
- [🏛️ 投资大师代理团队](#️-投资大师代理团队)
- [🚀 快速开始](#-快速开始)
  - [环境配置](#环境配置)
  - [基本使用](#基本使用)
  - [回测系统](#回测系统)
- [📊 数据源配置](#-数据源配置)
  - [本地新闻数据系统](#本地新闻数据系统)
  - [社交媒体数据收集](#社交媒体数据收集)
  - [API配置](#api配置)
- [🤖 LLM模型配置](#-llm模型配置)
  - [支持的模型提供商](#支持的模型提供商)
  - [API密钥配置](#api密钥配置)
- [📈 回测功能详解](#-回测功能详解)
  - [基本回测命令](#基本回测命令)
  - [高级功能](#高级功能)
  - [推理日志和准确性跟踪](#推理日志和准确性跟踪)
- [🔧 系统架构](#-系统架构)
- [📁 项目结构](#-项目结构)
- [🛠️ 故障排除](#️-故障排除)
- [🤝 贡献指南](#-贡献指南)
- [📄 许可证](#-许可证)

## 🚀 快速开始

### 环境配置

#### 使用Poetry（推荐）

1. **克隆仓库**
```bash
git clone https://github.com/virattt/ai-hedge-fund.git
cd ai-hedge-fund
```

2. **安装Poetry**
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

3. **安装依赖**
```bash
poetry install
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，添加必要的API密钥
```

#### 使用Docker

1. **构建Docker镜像**
```bash
# Linux/Mac
./run.sh build

# Windows
run.bat build
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，添加API密钥
```

### 基本使用

#### 运行AI对冲基金系统

```bash
# 使用Poetry
poetry run python src/main.py --ticker AAPL,MSFT,NVDA

# 使用Docker (Linux/Mac)
./run.sh --ticker AAPL,MSFT,NVDA main

# 使用Docker (Windows)
run.bat --ticker AAPL,MSFT,NVDA main
```

#### 显示推理过程

```bash
poetry run python src/main.py --ticker AAPL --show-reasoning
```

#### 指定时间范围

```bash
poetry run python src/main.py --ticker AAPL --start-date 2024-01-01 --end-date 2024-03-01
```

### 回测系统

#### 基本回测

```bash
# 使用Poetry
poetry run python src/backtester.py --ticker AAPL,MSFT,NVDA

# 使用Docker
./run.sh --ticker AAPL,MSFT,NVDA backtest
```

#### 高级回测功能

```bash
# 启用推理日志和准确性跟踪
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --track-accuracy \
  --save-reasoning \
  --save-input-data

# 使用本地新闻数据（推荐）
python src/backtester.py \
  --tickers NVDA \
  --start-date 2024-01-02 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage \
  --news-time-offset 1 \
  --track-accuracy \
  --save-reasoning \
  --save-input-data
```

## 📊 数据源配置

### 本地新闻数据系统

系统支持使用本地新闻数据，避免API速率限制，提高回测速度和数据一致性。

#### 支持的股票和数据源

| 股票代码 | Alpha Vantage | NewsAPI | Finnhub | 数据范围 |
|---------|---------------|---------|---------|----------|
| AAPL | ✅ | ✅ | ✅ | 2024-2025 |
| MSFT | ✅ | ✅ | ✅ | 2024-2025 |
| NVDA | ✅ | ❌ | ❌ | 2024-2025 |

#### 配置本地新闻数据

**方法1：环境变量**
```bash
export NEWS_USE_LOCAL_DATA=true
export NEWS_SOURCES=alpha_vantage,newsapi,finnhub
export NEWS_TIME_OFFSET_DAYS=1
```

**方法2：命令行参数**
```bash
python src/backtester.py \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --news-time-offset 1
```

**方法3：配置文件**
```json
{
  "default_settings": {
    "use_local_data": true,
    "time_offset_days": 1,
    "selected_sources": ["alpha_vantage", "newsapi", "finnhub"]
  }
}
```

#### 本地数据目录结构
```
项目根目录/
├── AAPL_alpha_news/           # AAPL Alpha Vantage新闻
│   ├── alpha_news_2024-01-01.json
│   └── ...
├── AAPL_news_api_news/        # AAPL NewsAPI新闻
│   ├── news_api_2024-01-01.json
│   └── ...
├── MSFT_alpha_news/           # MSFT Alpha Vantage新闻
├── NVDA_alpha_news/           # NVDA Alpha Vantage新闻
└── news_config.json           # 配置文件
```

### 社交媒体数据收集

系统包含StockTwits社交媒体数据收集器，用于获取股票相关的社交讨论数据。

#### 使用StockTwits收集器

```bash
# 基本使用
python stocktwits_data_collector.py

# 指定股票和时间范围
python stocktwits_data_collector.py \
  --tickers AAPL MSFT NVDA \
  --start-date 2025-01-01 \
  --end-date 2025-06-15

# 查看数据摘要
python stocktwits_data_collector.py --summary
```

#### 输出数据格式
```
social_media_data/
├── AAPL_social_media/
│   ├── stocktwits_2025-06-26.json
│   └── ...
├── MSFT_social_media/
└── NVDA_social_media/
```

### API配置

#### 必需的API密钥

```bash
# LLM API密钥（至少配置一个）
OPENAI_API_KEY=your-openai-api-key
GROQ_API_KEY1=your-first-groq-api-key
GROQ_API_KEY2=your-second-groq-api-key  # 可选，用于速率限制轮换
ANTHROPIC_API_KEY=your-anthropic-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key

# 金融数据API密钥
FINANCIAL_DATASETS_API_KEY=your-financial-datasets-api-key

# 新闻数据API密钥（使用本地数据时可选）
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
NEWSAPI_KEY=your-newsapi-key
FINNHUB_API_KEY=your-finnhub-key
```

#### 免费数据支持

- **AAPL、GOOGL、MSFT、NVDA、TSLA**：金融数据免费，无需API密钥
- **其他股票**：需要Financial Datasets API密钥

## 🤖 LLM模型配置

### 支持的模型提供商

| 提供商 | 模型示例 | 特点 | 配置要求 |
|--------|----------|------|----------|
| **OpenAI** | gpt-4o, gpt-4o-mini | 高质量分析，稳定性好 | OPENAI_API_KEY |
| **Groq** | llama-4-scout-17b, deepseek-v3 | 速度快，支持多密钥轮换 | GROQ_API_KEY1-4 |
| **Anthropic** | claude-3-sonnet, claude-3-haiku | 推理能力强 | ANTHROPIC_API_KEY |
| **DeepSeek** | deepseek-v3 | 成本低，中文支持好 | DEEPSEEK_API_KEY |
| **OpenRouter** | meta-llama/llama-4-scout:free | 免费模型支持 | OPENROUTER_API_KEY |
| **青云API** | claude, gemini, gpt | 国内访问稳定 | 使用https://api.qingyuntop.top/v1 |
| **Ollama** | 本地模型 | 完全离线，无API费用 | 本地安装Ollama |

### API密钥配置

#### 基本配置
```bash
# 主要LLM提供商（推荐配置多个）
OPENAI_API_KEY=sk-...
GROQ_API_KEY1=gsk_...
GROQ_API_KEY2=gsk_...  # 多密钥轮换，避免速率限制
ANTHROPIC_API_KEY=sk-ant-...
DEEPSEEK_API_KEY=sk-...

# OpenRouter免费模型
OPENROUTER_API_KEY=sk-or-v1-...
```

#### 高级配置
```bash
# GROQ API密钥轮换（推荐）
GROQ_API_KEY1=your-first-groq-key
GROQ_API_KEY2=your-second-groq-key
GROQ_API_KEY3=your-third-groq-key
GROQ_API_KEY4=your-fourth-groq-key

# 青云API配置（国内用户推荐）
# 在代码中自动使用 https://api.qingyuntop.tp/v1 端点
```

### 模型选择建议

#### 按用途选择
- **高质量分析**：gpt-4o, claude-3-sonnet
- **快速回测**：llama-4-scout-17b, deepseek-v3
- **成本控制**：gpt-4o-mini, deepseek-v3
- **离线使用**：Ollama本地模型
- **免费试用**：OpenRouter免费模型

#### 按场景选择
```bash
# 生产环境（高质量）
python src/backtester.py --model gpt-4o --provider OpenAI

# 开发测试（快速）
python src/backtester.py --model deepseek-v3 --provider Groq

# 离线环境
python src/backtester.py --ollama

# 免费试用
python src/backtester.py --model meta-llama/llama-4-scout:free --provider OpenRouter
```

## 📈 回测功能详解

### 基本回测命令

#### 简单回测
```bash
# 基本回测
python src/backtester.py --tickers AAPL

# 多股票回测
python src/backtester.py --tickers AAPL,MSFT,NVDA

# 指定时间范围
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31
```

#### 使用本地新闻数据（推荐）
```bash
# NVDA回测（推荐命令）
python src/backtester.py \
  --tickers NVDA \
  --track-accuracy \
  --save-reasoning \
  --save-input-data \
  --start-date 2024-01-02 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage \
  --news-time-offset 1

# AAPL多源新闻回测
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub \
  --track-accuracy \
  --save-reasoning
```

### 高级功能

#### 推理日志和准确性跟踪

**功能说明**：
- `--show-reasoning`：在终端显示AI代理的推理过程
- `--save-reasoning`：将推理过程保存到文件
- `--track-accuracy`：跟踪分析师信号的准确性
- `--save-input-data`：保存输入数据用于分析

**文件结构**：
```
reasoning_logs/
├── experiment_YYYY-MM-DD/          # 实验日期目录
│   ├── YYYY-MM-DD/                 # 交易日期目录
│   │   ├── YYYY-MM-DD_TICKER_agent_name_timestamp.json
│   │   └── ...
│   └── reflections/                # 反思分析日志
│       ├── reflection_YYYY-MM-DD.json
│       └── ...
└── accuracy_tracking/              # 准确性跟踪数据
    ├── accuracy_tracking_TICKER_DATERANGE_MODEL/
    └── ...
```

#### 确定性模式
```bash
# 确保结果可重现
python src/backtester.py \
  --tickers AAPL \
  --deterministic \
  --seed 42 \
  --experiment-id my_experiment
```

#### 选择特定分析师
```bash
# 只使用LLM智能分析师
python src/backtester.py \
  --tickers AAPL \
  --analysts fundamentals_analyst_ta,news_analyst,social_media_analyst
```

### 推理日志和准确性跟踪

#### 推理日志功能
- **终端显示**：实时查看AI代理的决策过程
- **文件保存**：结构化保存所有推理数据
- **自动组织**：按实验日期和交易日期组织文件
- **格式标准**：JSON格式，便于后续分析

#### 准确性跟踪功能
- **信号准确性**：跟踪每个分析师的信号准确率
- **性能评估**：计算各种性能指标
- **趋势分析**：分析准确性随时间的变化
- **对比分析**：比较不同分析师的表现

#### 使用示例
```bash
# 完整功能回测
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --track-accuracy \
  --save-reasoning \
  --save-input-data \
  --show-reasoning \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --model gpt-4o \
  --provider OpenAI
```

### 性能优化建议

#### 提高回测速度
1. **使用本地新闻数据**：避免API调用延迟
2. **选择快速模型**：如deepseek-v3, llama-4-scout-17b
3. **减少分析师数量**：只选择必要的分析师
4. **缩短时间范围**：先用短期数据测试

#### 提高分析质量
1. **使用高质量模型**：如gpt-4o, claude-3-sonnet
2. **启用推理日志**：便于调试和优化
3. **跟踪准确性**：监控分析师表现
4. **使用多源数据**：结合多个新闻源

## 🔧 系统架构

### 核心组件

#### 1. 多代理系统
- **投资大师代理**：模拟10位知名投资者的投资策略
- **专业分析师**：7个专业化分析代理，支持LLM智能分析
- **管理代理**：风险管理、投资组合管理、反思分析

#### 2. 工作流引擎
- **LangGraph状态图**：管理代理间的信息流和决策流程
- **状态管理**：跟踪投资组合状态和代理输出
- **并行处理**：支持多代理并行分析

#### 3. 数据层
- **多源数据集成**：金融数据、新闻数据、社交媒体数据
- **本地缓存系统**：减少API调用，提高性能
- **数据标准化**：统一的数据格式和接口

#### 4. LLM集成层
- **多提供商支持**：OpenAI、Groq、Anthropic、DeepSeek等
- **智能路由**：自动选择最佳模型和提供商
- **错误处理**：API限制处理和自动重试

#### 5. 反思学习机制
- **决策质量评估**：自动分析投资决策质量
- **持续改进**：基于历史决策优化未来决策
- **学习闭环**：形成"决策→反思→改进→决策"的循环

### 数据流架构

```mermaid
graph TD
    A[数据获取层] --> B[数据处理层]
    B --> C[代理分析层]
    C --> D[决策融合层]
    D --> E[风险管理层]
    E --> F[投资组合管理]
    F --> G[反思分析层]
    G --> H[学习优化]
    H --> C

    A1[金融数据API] --> A
    A2[本地新闻数据] --> A
    A3[社交媒体数据] --> A

    C1[投资大师代理] --> C
    C2[专业分析师] --> C

    F --> I[交易执行]
    F --> J[性能评估]
    F --> K[可视化报告]
```

### 技术栈

#### 后端技术
- **Python 3.8+**：主要编程语言
- **LangGraph**：工作流编排
- **Pydantic**：数据验证和序列化
- **FastAPI**：Web API服务
- **SQLite**：本地数据存储

#### 前端技术
- **React + TypeScript**：用户界面
- **React Flow**：代理流程可视化
- **Tailwind CSS**：样式框架
- **Chart.js**：数据可视化

#### AI/ML技术
- **LangChain**：LLM应用框架
- **OpenAI API**：GPT模型集成
- **Groq API**：高速推理
- **Anthropic API**：Claude模型
- **Ollama**：本地模型支持

### 扩展性设计

#### 代理扩展
- **插件化架构**：易于添加新的投资策略代理
- **标准接口**：统一的代理输入输出格式
- **配置驱动**：通过配置文件管理代理行为

#### 数据源扩展
- **适配器模式**：支持新数据源的快速集成
- **数据管道**：标准化的数据处理流程
- **缓存策略**：灵活的缓存和存储策略

#### 模型扩展
- **模型抽象层**：支持新LLM提供商的快速接入
- **负载均衡**：多模型并行处理
- **成本优化**：智能模型选择和API调用优化


## 📁 项目结构

```
ai-hedge-fund/
├── 📂 src/                          # 核心源代码
│   ├── 📂 agents/                   # AI代理实现
│   │   ├── 🤖 warren_buffett.py     # 巴菲特价值投资代理
│   │   ├── 🤖 charlie_munger.py     # 芒格多元思维代理
│   │   ├── 🤖 ben_graham.py         # 格雷厄姆价值投资代理
│   │   ├── 🤖 phil_fisher.py        # 费雪成长投资代理
│   │   ├── 🤖 peter_lynch.py        # 林奇实用投资代理
│   │   ├── 🤖 cathie_wood.py        # 伍德创新投资代理
│   │   ├── 🤖 bill_ackman.py        # 阿克曼激进投资代理
│   │   ├── 🤖 michael_burry.py      # 伯里逆势投资代理
│   │   ├── 🤖 aswath_damodaran.py   # 达摩达兰估值代理
│   │   ├── 🤖 stanley_druckenmiller.py # 德鲁肯米勒宏观代理
│   │   ├── 📊 fundamentals_analyst.py # 基本面分析师（支持LLM）
│   │   ├── 📊 market_analyst.py     # 市场分析师（支持LLM）
│   │   ├── 📊 news_analyst.py       # 新闻分析师（支持LLM）
│   │   ├── 📊 social_media_analyst.py # 社交媒体分析师（支持LLM）
│   │   ├── 📊 technicals.py         # 技术分析代理
│   │   ├── 📊 sentiment.py          # 情绪分析代理
│   │   ├── 📊 valuation.py          # 估值分析代理
│   │   ├── 🛡️ risk_manager.py       # 风险管理代理
│   │   ├── 💼 portfolio_manager.py  # 投资组合管理代理
│   │   └── 🔄 reflection_analyst.py # 反思分析代理
│   ├── 📂 config/                   # 配置管理
│   │   └── 📰 news_config.py        # 新闻数据配置
│   ├── 📂 data/                     # 数据处理
│   │   ├── 💾 cache.py              # 本地缓存系统
│   │   └── 📋 models.py             # 数据模型定义
│   ├── 📂 tools/                    # 工具模块
│   │   ├── 🔌 api.py                # API集成工具
│   │   └── 📰 local_news_reader.py  # 本地新闻读取器
│   ├── 📂 llm/                      # LLM集成
│   │   └── 🧠 models.py             # LLM模型定义
│   ├── 📂 utils/                    # 实用工具
│   │   ├── 👥 analysts.py           # 分析师配置
│   │   ├── 🎨 display.py            # 结果显示
│   │   └── 📊 visualize.py          # 数据可视化
│   ├── 🚀 main.py                   # 主程序入口
│   └── 📈 backtester.py             # 回测系统
├── 📂 app/                          # Web应用
│   ├── 📂 backend/                  # 后端API
│   │   ├── 📂 routes/               # API路由
│   │   ├── 📂 services/             # 业务服务
│   │   └── 🌐 main.py               # FastAPI应用
│   └── 📂 frontend/                 # 前端界面
│       ├── 📂 src/                  # React源代码
│       │   ├── 📂 components/       # React组件
│       │   └── 📂 services/         # 前端服务
│       └── 📦 package.json          # 前端依赖
├── 📂 docs/                         # 文档目录
│   ├── 📖 backtester_usage_guide.md # 回测使用指南
│   ├── 📖 groq_api_rotation_guide.md # GROQ API轮换指南
│   └── 📖 openrouter_integration_guide.md # OpenRouter集成指南
├── 📂 data/                         # 数据目录
│   ├── 📂 AAPL_alpha_news/          # AAPL Alpha Vantage新闻
│   ├── 📂 MSFT_alpha_news/          # MSFT Alpha Vantage新闻
│   ├── 📂 NVDA_alpha_news/          # NVDA Alpha Vantage新闻
│   └── 📂 social_media_data/        # 社交媒体数据
├── 📂 reasoning_logs/               # 推理日志
│   ├── 📂 experiment_YYYY-MM-DD/    # 实验日志
│   ├── 📂 accuracy_tracking/        # 准确性跟踪
│   └── 📂 reflections/              # 反思分析日志
├── 🐳 Dockerfile                    # Docker构建文件
├── 🐳 docker-compose.yml            # Docker编排文件
├── 📋 pyproject.toml                # Poetry项目配置
├── 🔧 .env.example                  # 环境变量示例
├── 📊 stocktwits_data_collector.py  # StockTwits数据收集器
└── 📖 README.md                     # 项目说明文档
```

### 核心文件说明

#### 🚀 主要入口点
- **`src/main.py`**: AI对冲基金主程序，处理命令行参数，创建代理工作流
- **`src/backtester.py`**: 回测系统入口，模拟历史交易策略表现
- **`app/backend/main.py`**: Web API后端服务
- **`app/frontend/src/App.tsx`**: React前端应用主组件

#### 🤖 代理系统
- **投资大师代理**: 模拟10位知名投资者的投资策略和决策风格
- **专业分析师**: 7个专业化分析代理，支持传统算法和LLM智能分析
- **管理代理**: 风险管理、投资组合管理、反思分析等核心功能

#### 📊 数据处理
- **`src/tools/api.py`**: 金融数据API集成，支持多种数据源
- **`src/tools/local_news_reader.py`**: 本地新闻数据读取器
- **`src/data/cache.py`**: 本地缓存系统，减少API调用
- **`src/config/news_config.py`**: 新闻数据配置管理

#### 🧠 LLM集成
- **`src/llm/models.py`**: LLM模型定义和工具
- **`src/utils/llm.py`**: LLM调用工具和提示工程
- **多提供商支持**: OpenAI、Groq、Anthropic、DeepSeek等

#### 📈 回测和分析
- **推理日志系统**: 详细记录AI代理的决策过程
- **准确性跟踪**: 监控分析师信号的准确性
- **反思学习**: 持续改进决策质量的学习机制
- **性能评估**: 全面的回测性能分析和可视化

## 🛠️ 故障排除

### 常见问题及解决方案

#### 🔑 API密钥相关问题

**问题1：API密钥错误或无效**
```bash
# 检查环境变量
echo $OPENAI_API_KEY
echo $GROQ_API_KEY1

# 验证.env文件
cat .env | grep API_KEY
```

**解决方案**：
- 确保API密钥格式正确
- 检查密钥是否已过期
- 验证密钥权限和配额

**问题2：GROQ API速率限制**
```
Error: Rate limit exceeded (429)
```

**解决方案**：
- 配置多个GROQ API密钥（GROQ_API_KEY1-4）
- 增加请求间隔时间
- 使用其他LLM提供商

#### 📰 本地新闻数据问题

**问题3：找不到本地新闻数据文件**
```
⚠️ Alpha Vantage数据文件不存在: AAPL_alpha_news/alpha_news_2024-01-01.json
```

**解决方案**：
```python
# 检查数据可用性
from src.config.news_config import news_config
availability = news_config.check_local_data_availability()
print(availability)

# 验证目录结构
import os
for ticker in ['AAPL', 'MSFT', 'NVDA']:
    for source in ['alpha_news', 'news_api_news', 'finnhub_news']:
        dir_path = f"{ticker}_{source}"
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}: {len(os.listdir(dir_path))} 文件")
        else:
            print(f"❌ {dir_path}: 目录不存在")
```

**问题4：新闻配置导入失败**
```
⚠️ 无法导入本地新闻配置，使用API模式
```

**解决方案**：
- 检查配置文件格式
- 验证环境变量设置
- 使用默认配置

#### 🧠 LLM模型问题

**问题5：模型响应超时或错误**
```
Error: Request timeout / Model not available
```

**解决方案**：
```python
# 测试不同模型
python src/backtester.py --model gpt-4o-mini --provider OpenAI
python src/backtester.py --model deepseek-v3 --provider Groq
python src/backtester.py --ollama  # 使用本地模型
```

**问题6：内存不足**
```
MemoryError: Unable to allocate memory
```

**解决方案**：
- 减少回测时间范围
- 降低新闻数量限制
- 使用更少的股票代码
- 启用本地数据模式

#### 🐳 Docker相关问题

**问题7：Docker构建失败**
```bash
# 清理Docker缓存
docker system prune -a

# 重新构建
./run.sh build
```

**问题8：容器启动失败**
```bash
# 检查日志
docker logs ai-hedge-fund

# 检查端口占用
netstat -tulpn | grep :8000
```

### 调试工具和命令

#### 配置检查
```python
# 检查新闻配置状态
from src.config.news_config import print_news_config_status
print_news_config_status()

# 测试数据读取
from src.tools.local_news_reader import get_local_multi_source_news
news = get_local_multi_source_news(ticker="AAPL", limit=1)
print(f"测试结果: {len(news)} 条新闻")
```

#### 性能监控
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG

# 监控API调用
python src/backtester.py --tickers AAPL --show-reasoning --save-reasoning
```

#### 数据验证
```python
# 验证本地数据完整性
import json
import os

def validate_news_data(ticker, date):
    """验证特定日期的新闻数据"""
    sources = ['alpha_news', 'news_api_news', 'finnhub_news']
    for source in sources:
        file_path = f"{ticker}_{source}/{source.replace('_news', '')}_{date}.json"
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"✅ {source}: {len(data)} 条新闻")
            except Exception as e:
                print(f"❌ {source}: 数据格式错误 - {e}")
        else:
            print(f"⚠️ {source}: 文件不存在")

# 使用示例
validate_news_data("AAPL", "2024-01-15")
```

### 性能优化建议

#### 提高回测速度
1. **使用本地数据**：避免API调用延迟
2. **选择快速模型**：deepseek-v3, llama-4-scout-17b
3. **并行处理**：减少分析师数量，使用多线程
4. **缓存优化**：启用数据缓存机制

#### 降低成本
1. **免费模型**：使用OpenRouter免费模型
2. **本地模型**：使用Ollama本地推理
3. **API轮换**：配置多个API密钥
4. **智能调用**：只在必要时使用高质量模型

#### 提高准确性
1. **高质量模型**：gpt-4o, claude-3-sonnet
2. **多源数据**：结合多个新闻源
3. **推理日志**：启用详细推理记录
4. **准确性跟踪**：监控分析师表现

## 🤝 贡献指南

我们欢迎社区贡献！无论是bug修复、新功能开发还是文档改进，都非常感谢您的参与。

### 🚀 如何贡献

#### 1. 准备开发环境

```bash
# Fork并克隆仓库
git clone https://github.com/your-username/ai-hedge-fund.git
cd ai-hedge-fund

# 安装开发依赖
poetry install --with dev

# 安装pre-commit钩子
pre-commit install
```

#### 2. 创建功能分支

```bash
# 从main分支创建新分支
git checkout -b feature/your-feature-name

# 或者修复bug
git checkout -b fix/bug-description
```

#### 3. 开发和测试

```bash
# 运行测试
poetry run pytest

# 代码格式化
poetry run black src/
poetry run isort src/

# 类型检查
poetry run mypy src/
```

#### 4. 提交更改

```bash
# 添加更改
git add .

# 提交（遵循约定式提交格式）
git commit -m "feat: add new investment strategy agent"
git commit -m "fix: resolve API rate limit issue"
git commit -m "docs: update installation guide"
```

#### 5. 创建Pull Request

- 推送分支到您的fork
- 在GitHub上创建Pull Request
- 填写PR模板，描述您的更改
- 等待代码审查和反馈

### 📋 贡献类型

#### 🐛 Bug修复
- 修复现有功能的问题
- 改进错误处理
- 性能优化

#### ✨ 新功能
- 新的投资策略代理
- 新的数据源集成
- 新的分析工具

#### 📚 文档改进
- API文档
- 使用指南
- 代码注释

#### 🧪 测试
- 单元测试
- 集成测试
- 性能测试

### 🎯 开发重点领域

#### 1. 新投资策略代理
我们欢迎添加新的投资大师代理，例如：
- **Ray Dalio** - 桥水基金创始人，全天候投资策略
- **Howard Marks** - 橡树资本联合创始人，逆向投资专家
- **Joel Greenblatt** - 神奇公式投资法创始人
- **David Einhorn** - 绿光资本创始人，价值投资者

#### 2. 数据源扩展
- 更多新闻数据源（Bloomberg, Reuters等）
- 另类数据（卫星数据、信用卡交易等）
- 国际市场数据支持
- 加密货币数据集成

#### 3. 分析工具增强
- 更多技术指标
- 宏观经济指标分析
- ESG（环境、社会、治理）评分
- 行业轮动分析

#### 4. 用户体验改进
- 更好的可视化界面
- 实时数据流支持
- 移动端适配
- 多语言支持

### 📝 代码规范

#### Python代码风格
```python
# 使用类型提示
def calculate_sharpe_ratio(returns: List[float], risk_free_rate: float = 0.02) -> float:
    """计算夏普比率

    Args:
        returns: 收益率列表
        risk_free_rate: 无风险利率

    Returns:
        夏普比率
    """
    pass

# 使用Pydantic模型
class InvestmentSignal(BaseModel):
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(ge=0, le=100)
    reasoning: str
    timestamp: datetime = Field(default_factory=datetime.now)
```

#### 提交信息格式
遵循[约定式提交](https://www.conventionalcommits.org/zh-hans/)格式：

```
<类型>[可选的作用域]: <描述>

[可选的正文]

[可选的脚注]
```

**类型**：
- `feat`: 新功能
- `fix`: bug修复
- `docs`: 文档更新
- `style`: 代码格式（不影响功能）
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**：
```
feat(agents): add Ray Dalio investment strategy agent

- Implement All Weather portfolio strategy
- Add risk parity calculation
- Include macroeconomic analysis

Closes #123
```

### 🧪 测试指南

#### 单元测试
```python
import pytest
from src.agents.warren_buffett import WarrenBuffettAgent

def test_warren_buffett_signal_generation():
    """测试巴菲特代理信号生成"""
    agent = WarrenBuffettAgent()

    # 模拟输入数据
    mock_data = {
        "ticker": "AAPL",
        "financial_metrics": {...},
        "news_data": [...]
    }

    # 测试信号生成
    signal = agent.generate_signal(mock_data)

    assert signal.signal in ["bullish", "bearish", "neutral"]
    assert 0 <= signal.confidence <= 100
    assert len(signal.reasoning) > 0
```

#### 集成测试
```python
def test_full_backtest_pipeline():
    """测试完整回测流程"""
    from src.backtester import Backtester
    from src.main import run_hedge_fund

    backtester = Backtester(
        agent=run_hedge_fund,
        tickers=["AAPL"],
        start_date="2024-01-01",
        end_date="2024-01-05",
        initial_capital=100000
    )

    performance = backtester.run_backtest()

    assert "sharpe_ratio" in performance
    assert "max_drawdown" in performance
```

### 🏆 贡献者认可

我们重视每一位贡献者的努力：

- **代码贡献者**：在README中列出
- **文档贡献者**：在文档页面中致谢
- **Bug报告者**：在发布说明中感谢
- **功能建议者**：在功能实现时致谢

### 📞 获取帮助

如果您在贡献过程中遇到问题：

1. **查看文档**：检查现有文档和示例
2. **搜索Issues**：查看是否有类似问题
3. **创建Issue**：描述您的问题或建议
4. **加入讨论**：参与GitHub Discussions

### 🎉 感谢贡献者

感谢所有为这个项目做出贡献的开发者、测试者、文档编写者和用户！

---

**记住**：每个贡献都很重要，无论大小。我们期待您的参与！

## 📄 许可证

本项目采用 **MIT 许可证** - 详情请参阅 [LICENSE](LICENSE) 文件。

### MIT 许可证摘要

✅ **允许的使用**：
- 商业使用
- 修改
- 分发
- 私人使用

⚠️ **条件**：
- 包含许可证和版权声明

❌ **限制**：
- 不提供责任保证
- 不提供保修

### 第三方许可证

本项目使用了以下开源库，请遵守相应的许可证：

- **LangChain** - MIT License
- **FastAPI** - MIT License
- **React** - MIT License
- **OpenAI Python** - Apache 2.0 License
- **Anthropic SDK** - MIT License
- **Pydantic** - MIT License

---

## 🌟 致谢

### 核心贡献者
- **[@virattt](https://github.com/virattt)** - 项目创始人和主要维护者

### 特别感谢
- **投资大师们** - Warren Buffett, Charlie Munger, Ben Graham等，他们的投资智慧启发了这个项目
- **开源社区** - 感谢所有开源库的维护者和贡献者
- **AI/ML社区** - 感谢推动人工智能发展的研究者和开发者

### 数据提供商
- **Financial Datasets** - 金融数据API
- **Alpha Vantage** - 新闻和市场数据
- **NewsAPI** - 新闻数据服务
- **Finnhub** - 实时金融数据
- **StockTwits** - 社交媒体数据

---

## 📞 联系我们

### 项目相关
- **GitHub Issues**: [报告问题或建议功能](https://github.com/virattt/ai-hedge-fund/issues)
- **GitHub Discussions**: [参与社区讨论](https://github.com/virattt/ai-hedge-fund/discussions)
- **Twitter**: [@virattt](https://twitter.com/virattt)

### 商业合作
如果您对商业合作、企业级支持或定制开发感兴趣，请通过以下方式联系：
- 创建GitHub Issue并标记为 `business-inquiry`
- 通过Twitter私信联系

---

## 🎯 项目愿景

我们的愿景是创建一个开放、透明、教育性的AI投资研究平台，让每个人都能：

1. **学习投资智慧** - 通过模拟大师级投资者的思维过程
2. **理解AI应用** - 探索人工智能在金融领域的实际应用
3. **提升投资素养** - 通过实践和分析提高投资决策能力
4. **推动创新** - 为AI+金融领域的发展贡献力量

### 未来发展方向

#### 短期目标（3-6个月）
- 🎯 添加更多投资大师代理（Ray Dalio, Howard Marks等）
- 📊 增强数据可视化和报告功能
- 🌍 支持国际市场和多币种
- 📱 开发移动端应用

#### 中期目标（6-12个月）
- 🤖 集成更多AI模型和技术
- 📈 实时交易信号和警报系统
- 🔗 区块链和DeFi集成
- 🎓 在线教育和培训模块

#### 长期目标（1-2年）
- 🏢 企业级解决方案
- 🌐 全球社区和生态系统
- 🔬 学术研究合作
- 🚀 商业化产品和服务

---

## 🔮 结语

AI对冲基金项目代表了人工智能与金融投资结合的一次有意义的探索。我们相信，通过开源协作和社区驱动的方式，可以创造出真正有价值的教育和研究工具。

**记住**：投资有风险，决策需谨慎。本项目仅供学习和研究使用，不构成投资建议。

感谢您对AI对冲基金项目的关注和支持！让我们一起探索AI与投资的无限可能。

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个Star！ ⭐**

[![GitHub stars](https://img.shields.io/github/stars/virattt/ai-hedge-fund?style=social)](https://github.com/virattt/ai-hedge-fund/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/virattt/ai-hedge-fund?style=social)](https://github.com/virattt/ai-hedge-fund/network/members)
[![Twitter Follow](https://img.shields.io/twitter/follow/virattt?style=social)](https://twitter.com/virattt)

**让AI投资智慧触手可及 🚀**

</div>
