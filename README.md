# AI 对冲基金 - 智能投资决策系统

这是一个基于人工智能的对冲基金概念验证项目，旨在探索使用大语言模型(LLM)进行智能投资决策。该系统集成了多个专业化的AI代理，模拟知名投资者的投资策略，并结合多源数据分析来做出投资决策。

**⚠️ 重要声明：本项目仅供教育和研究目的使用，不适用于实际交易或投资。**

## 🎯 系统特色

- **🤖 多代理协作**：17个专业化AI代理，模拟知名投资者策略
- **📊 多维度分析**：基本面、技术面、情绪面、估值分析
- **📰 多源数据支持**：本地新闻数据、社交媒体数据、财务数据
- **🧠 LLM智能分析**：使用大语言模型进行深度推理和分析
- **📈 完整回测系统**：历史数据回测，性能评估和可视化
- **🔄 反思学习机制**：持续改进决策质量
- **🌐 Web界面**：直观的用户界面和实时进度跟踪

<img width="1042" alt="Screenshot 2025-03-22 at 6 19 07 PM" src="https://github.com/user-attachments/assets/cbae3dcf-b571-490d-b0ad-3f0f035ac0d4" />

## 🏛️ 投资大师代理团队

### 价值投资派
- **Warren Buffett** - 奥马哈的先知，寻找具有经济护城河的优质公司
- **Charlie Munger** - 巴菲特的合伙人，运用多元思维模型
- **Ben Graham** - 价值投资之父，坚持安全边际原则
- **Michael Burry** - 《大空头》逆势投资者，寻找深度价值

### 成长投资派
- **Phil Fisher** - 成长型投资先驱，深度"小道消息"研究
- **Peter Lynch** - 实用投资者，寻找日常生活中的"十倍股"
- **Cathie Wood** - 创新投资女王，专注颠覆性技术

### 激进投资派
- **Bill Ackman** - 激进投资者，推动企业变革
- **Stanley Druckenmiller** - 宏观投资传奇，寻找不对称机会

### 估值专家
- **Aswath Damodaran** - 纽约大学估值专家，严格的DCF分析

### 专业分析师
- **基本面分析师** - 财务数据深度分析（支持LLM智能分析）
- **技术分析师** - 价格趋势和技术指标分析
- **新闻分析师** - 新闻情感和事件影响分析（支持LLM智能分析）
- **社交媒体分析师** - 社交情感和公众舆论分析（支持LLM智能分析）
- **估值分析师** - 内在价值计算和相对估值
- **风险管理器** - 投资组合风险评估和控制
- **投资组合管理器** - 最终决策制定和交易执行
- **反思分析师** - 决策质量分析和持续改进

[![Twitter Follow](https://img.shields.io/twitter/follow/virattt?style=social)](https://twitter.com/virattt)

## ⚠️ 免责声明

本项目**仅供教育和研究目的**。

- ❌ 不适用于实际交易或投资
- ❌ 不提供投资建议或保证
- ❌ 创建者对财务损失不承担责任
- ✅ 投资决策请咨询专业财务顾问
- ⚠️ 过去的表现不代表未来的结果

使用本软件即表示您同意仅将其用于学习和研究目的。

## 📚 目录
- [🎯 系统特色](#-系统特色)
- [🏛️ 投资大师代理团队](#️-投资大师代理团队)
- [🚀 快速开始](#-快速开始)
  - [环境配置](#环境配置)
  - [基本使用](#基本使用)
  - [回测系统](#回测系统)
- [📊 数据源配置](#-数据源配置)
  - [本地新闻数据系统](#本地新闻数据系统)
  - [社交媒体数据收集](#社交媒体数据收集)
  - [API配置](#api配置)
- [🤖 LLM模型配置](#-llm模型配置)
  - [支持的模型提供商](#支持的模型提供商)
  - [API密钥配置](#api密钥配置)
- [📈 回测功能详解](#-回测功能详解)
  - [基本回测命令](#基本回测命令)
  - [高级功能](#高级功能)
  - [推理日志和准确性跟踪](#推理日志和准确性跟踪)
- [🔧 系统架构](#-系统架构)
- [📁 项目结构](#-项目结构)
- [🛠️ 故障排除](#️-故障排除)
- [🤝 贡献指南](#-贡献指南)
- [📄 许可证](#-许可证)

## 🚀 快速开始

### 环境配置

#### 使用Poetry（推荐）

1. **克隆仓库**
```bash
git clone https://github.com/virattt/ai-hedge-fund.git
cd ai-hedge-fund
```

2. **安装Poetry**
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

3. **安装依赖**
```bash
poetry install
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，添加必要的API密钥
```

#### 使用Docker

1. **构建Docker镜像**
```bash
# Linux/Mac
./run.sh build

# Windows
run.bat build
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑.env文件，添加API密钥
```

### 基本使用

#### 运行AI对冲基金系统

```bash
# 使用Poetry
poetry run python src/main.py --ticker AAPL,MSFT,NVDA

# 使用Docker (Linux/Mac)
./run.sh --ticker AAPL,MSFT,NVDA main

# 使用Docker (Windows)
run.bat --ticker AAPL,MSFT,NVDA main
```

#### 显示推理过程

```bash
poetry run python src/main.py --ticker AAPL --show-reasoning
```

#### 指定时间范围

```bash
poetry run python src/main.py --ticker AAPL --start-date 2024-01-01 --end-date 2024-03-01
```

### 回测系统

#### 基本回测

```bash
# 使用Poetry
poetry run python src/backtester.py --ticker AAPL,MSFT,NVDA

# 使用Docker
./run.sh --ticker AAPL,MSFT,NVDA backtest
```

#### 高级回测功能

```bash
# 启用推理日志和准确性跟踪
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --track-accuracy \
  --save-reasoning \
  --save-input-data

# 使用本地新闻数据（推荐）
python src/backtester.py \
  --tickers NVDA \
  --start-date 2024-01-02 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage \
  --news-time-offset 1 \
  --track-accuracy \
  --save-reasoning \
  --save-input-data
```

## 📊 数据源配置

### 本地新闻数据系统

系统支持使用本地新闻数据，避免API速率限制，提高回测速度和数据一致性。

#### 支持的股票和数据源

| 股票代码 | Alpha Vantage | NewsAPI | Finnhub | 数据范围 |
|---------|---------------|---------|---------|----------|
| AAPL | ✅ | ✅ | ✅ | 2024-2025 |
| MSFT | ✅ | ✅ | ✅ | 2024-2025 |
| NVDA | ✅ | ❌ | ❌ | 2024-2025 |

#### 配置本地新闻数据

**方法1：环境变量**
```bash
export NEWS_USE_LOCAL_DATA=true
export NEWS_SOURCES=alpha_vantage,newsapi,finnhub
export NEWS_TIME_OFFSET_DAYS=1
```

**方法2：命令行参数**
```bash
python src/backtester.py \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --news-time-offset 1
```

**方法3：配置文件**
```json
{
  "default_settings": {
    "use_local_data": true,
    "time_offset_days": 1,
    "selected_sources": ["alpha_vantage", "newsapi", "finnhub"]
  }
}
```

#### 本地数据目录结构
```
项目根目录/
├── AAPL_alpha_news/           # AAPL Alpha Vantage新闻
│   ├── alpha_news_2024-01-01.json
│   └── ...
├── AAPL_news_api_news/        # AAPL NewsAPI新闻
│   ├── news_api_2024-01-01.json
│   └── ...
├── MSFT_alpha_news/           # MSFT Alpha Vantage新闻
├── NVDA_alpha_news/           # NVDA Alpha Vantage新闻
└── news_config.json           # 配置文件
```

### 社交媒体数据收集

系统包含StockTwits社交媒体数据收集器，用于获取股票相关的社交讨论数据。

#### 使用StockTwits收集器

```bash
# 基本使用
python stocktwits_data_collector.py

# 指定股票和时间范围
python stocktwits_data_collector.py \
  --tickers AAPL MSFT NVDA \
  --start-date 2025-01-01 \
  --end-date 2025-06-15

# 查看数据摘要
python stocktwits_data_collector.py --summary
```

#### 输出数据格式
```
social_media_data/
├── AAPL_social_media/
│   ├── stocktwits_2025-06-26.json
│   └── ...
├── MSFT_social_media/
└── NVDA_social_media/
```

### API配置

#### 必需的API密钥

```bash
# LLM API密钥（至少配置一个）
OPENAI_API_KEY=your-openai-api-key
GROQ_API_KEY1=your-first-groq-api-key
GROQ_API_KEY2=your-second-groq-api-key  # 可选，用于速率限制轮换
ANTHROPIC_API_KEY=your-anthropic-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key

# 金融数据API密钥
FINANCIAL_DATASETS_API_KEY=your-financial-datasets-api-key

# 新闻数据API密钥（使用本地数据时可选）
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
NEWSAPI_KEY=your-newsapi-key
FINNHUB_API_KEY=your-finnhub-key
```

#### 免费数据支持

- **AAPL、GOOGL、MSFT、NVDA、TSLA**：金融数据免费，无需API密钥
- **其他股票**：需要Financial Datasets API密钥

## 🤖 LLM模型配置

### 支持的模型提供商

| 提供商 | 模型示例 | 特点 | 配置要求 |
|--------|----------|------|----------|
| **OpenAI** | gpt-4o, gpt-4o-mini | 高质量分析，稳定性好 | OPENAI_API_KEY |
| **Groq** | llama-4-scout-17b, deepseek-v3 | 速度快，支持多密钥轮换 | GROQ_API_KEY1-4 |
| **Anthropic** | claude-3-sonnet, claude-3-haiku | 推理能力强 | ANTHROPIC_API_KEY |
| **DeepSeek** | deepseek-v3 | 成本低，中文支持好 | DEEPSEEK_API_KEY |
| **OpenRouter** | meta-llama/llama-4-scout:free | 免费模型支持 | OPENROUTER_API_KEY |
| **青云API** | claude, gemini, gpt | 国内访问稳定 | 使用https://api.qingyuntop.top/v1 |
| **Ollama** | 本地模型 | 完全离线，无API费用 | 本地安装Ollama |

### API密钥配置

#### 基本配置
```bash
# 主要LLM提供商（推荐配置多个）
OPENAI_API_KEY=sk-...
GROQ_API_KEY1=gsk_...
GROQ_API_KEY2=gsk_...  # 多密钥轮换，避免速率限制
ANTHROPIC_API_KEY=sk-ant-...
DEEPSEEK_API_KEY=sk-...

# OpenRouter免费模型
OPENROUTER_API_KEY=sk-or-v1-...
```

#### 高级配置
```bash
# GROQ API密钥轮换（推荐）
GROQ_API_KEY1=your-first-groq-key
GROQ_API_KEY2=your-second-groq-key
GROQ_API_KEY3=your-third-groq-key
GROQ_API_KEY4=your-fourth-groq-key

# 青云API配置（国内用户推荐）
# 在代码中自动使用 https://api.qingyuntop.tp/v1 端点
```

### 模型选择建议

#### 按用途选择
- **高质量分析**：gpt-4o, claude-3-sonnet
- **快速回测**：llama-4-scout-17b, deepseek-v3
- **成本控制**：gpt-4o-mini, deepseek-v3
- **离线使用**：Ollama本地模型
- **免费试用**：OpenRouter免费模型

#### 按场景选择
```bash
# 生产环境（高质量）
python src/backtester.py --model gpt-4o --provider OpenAI

# 开发测试（快速）
python src/backtester.py --model deepseek-v3 --provider Groq

# 离线环境
python src/backtester.py --ollama

# 免费试用
python src/backtester.py --model meta-llama/llama-4-scout:free --provider OpenRouter
```

## 📈 回测功能详解

### 基本回测命令

#### 简单回测
```bash
# 基本回测
python src/backtester.py --tickers AAPL

# 多股票回测
python src/backtester.py --tickers AAPL,MSFT,NVDA

# 指定时间范围
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31
```

#### 使用本地新闻数据（推荐）
```bash
# NVDA回测（推荐命令）
python src/backtester.py \
  --tickers NVDA \
  --track-accuracy \
  --save-reasoning \
  --save-input-data \
  --start-date 2024-01-02 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage \
  --news-time-offset 1

# AAPL多源新闻回测
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --use-local-news \
  --news-sources alpha_vantage,newsapi,finnhub \
  --track-accuracy \
  --save-reasoning
```

### 高级功能

#### 推理日志和准确性跟踪

**功能说明**：
- `--show-reasoning`：在终端显示AI代理的推理过程
- `--save-reasoning`：将推理过程保存到文件
- `--track-accuracy`：跟踪分析师信号的准确性
- `--save-input-data`：保存输入数据用于分析

**文件结构**：
```
reasoning_logs/
├── experiment_YYYY-MM-DD/          # 实验日期目录
│   ├── YYYY-MM-DD/                 # 交易日期目录
│   │   ├── YYYY-MM-DD_TICKER_agent_name_timestamp.json
│   │   └── ...
│   └── reflections/                # 反思分析日志
│       ├── reflection_YYYY-MM-DD.json
│       └── ...
└── accuracy_tracking/              # 准确性跟踪数据
    ├── accuracy_tracking_TICKER_DATERANGE_MODEL/
    └── ...
```

#### 确定性模式
```bash
# 确保结果可重现
python src/backtester.py \
  --tickers AAPL \
  --deterministic \
  --seed 42 \
  --experiment-id my_experiment
```

#### 选择特定分析师
```bash
# 只使用LLM智能分析师
python src/backtester.py \
  --tickers AAPL \
  --analysts fundamentals_analyst_ta,news_analyst,social_media_analyst
```

### 推理日志和准确性跟踪

#### 推理日志功能
- **终端显示**：实时查看AI代理的决策过程
- **文件保存**：结构化保存所有推理数据
- **自动组织**：按实验日期和交易日期组织文件
- **格式标准**：JSON格式，便于后续分析

#### 准确性跟踪功能
- **信号准确性**：跟踪每个分析师的信号准确率
- **性能评估**：计算各种性能指标
- **趋势分析**：分析准确性随时间的变化
- **对比分析**：比较不同分析师的表现

#### 使用示例
```bash
# 完整功能回测
python src/backtester.py \
  --tickers AAPL \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --track-accuracy \
  --save-reasoning \
  --save-input-data \
  --show-reasoning \
  --use-local-news \
  --news-sources alpha_vantage,newsapi \
  --model gpt-4o \
  --provider OpenAI
```

### 性能优化建议

#### 提高回测速度
1. **使用本地新闻数据**：避免API调用延迟
2. **选择快速模型**：如deepseek-v3, llama-4-scout-17b
3. **减少分析师数量**：只选择必要的分析师
4. **缩短时间范围**：先用短期数据测试

#### 提高分析质量
1. **使用高质量模型**：如gpt-4o, claude-3-sonnet
2. **启用推理日志**：便于调试和优化
3. **跟踪准确性**：监控分析师表现
4. **使用多源数据**：结合多个新闻源

## 🔧 系统架构

### 核心组件

#### 1. 多代理系统
- **投资大师代理**：模拟10位知名投资者的投资策略
- **专业分析师**：7个专业化分析代理，支持LLM智能分析
- **管理代理**：风险管理、投资组合管理、反思分析

#### 2. 工作流引擎
- **LangGraph状态图**：管理代理间的信息流和决策流程
- **状态管理**：跟踪投资组合状态和代理输出
- **并行处理**：支持多代理并行分析

#### 3. 数据层
- **多源数据集成**：金融数据、新闻数据、社交媒体数据
- **本地缓存系统**：减少API调用，提高性能
- **数据标准化**：统一的数据格式和接口

#### 4. LLM集成层
- **多提供商支持**：OpenAI、Groq、Anthropic、DeepSeek等
- **智能路由**：自动选择最佳模型和提供商
- **错误处理**：API限制处理和自动重试

#### 5. 反思学习机制
- **决策质量评估**：自动分析投资决策质量
- **持续改进**：基于历史决策优化未来决策
- **学习闭环**：形成"决策→反思→改进→决策"的循环

### 数据流架构

```mermaid
graph TD
    A[数据获取层] --> B[数据处理层]
    B --> C[代理分析层]
    C --> D[决策融合层]
    D --> E[风险管理层]
    E --> F[投资组合管理]
    F --> G[反思分析层]
    G --> H[学习优化]
    H --> C

    A1[金融数据API] --> A
    A2[本地新闻数据] --> A
    A3[社交媒体数据] --> A

    C1[投资大师代理] --> C
    C2[专业分析师] --> C

    F --> I[交易执行]
    F --> J[性能评估]
    F --> K[可视化报告]
```

### 技术栈

#### 后端技术
- **Python 3.8+**：主要编程语言
- **LangGraph**：工作流编排
- **Pydantic**：数据验证和序列化
- **FastAPI**：Web API服务
- **SQLite**：本地数据存储

#### 前端技术
- **React + TypeScript**：用户界面
- **React Flow**：代理流程可视化
- **Tailwind CSS**：样式框架
- **Chart.js**：数据可视化

#### AI/ML技术
- **LangChain**：LLM应用框架
- **OpenAI API**：GPT模型集成
- **Groq API**：高速推理
- **Anthropic API**：Claude模型
- **Ollama**：本地模型支持

### 扩展性设计

#### 代理扩展
- **插件化架构**：易于添加新的投资策略代理
- **标准接口**：统一的代理输入输出格式
- **配置驱动**：通过配置文件管理代理行为

#### 数据源扩展
- **适配器模式**：支持新数据源的快速集成
- **数据管道**：标准化的数据处理流程
- **缓存策略**：灵活的缓存和存储策略

#### 模型扩展
- **模型抽象层**：支持新LLM提供商的快速接入
- **负载均衡**：多模型并行处理
- **成本优化**：智能模型选择和API调用优化


## 📁 项目结构

```
ai-hedge-fund/
├── 📂 src/                          # 核心源代码
│   ├── 📂 agents/                   # AI代理实现
│   │   ├── 🤖 warren_buffett.py     # 巴菲特价值投资代理
│   │   ├── 🤖 charlie_munger.py     # 芒格多元思维代理
│   │   ├── 🤖 ben_graham.py         # 格雷厄姆价值投资代理
│   │   ├── 🤖 phil_fisher.py        # 费雪成长投资代理
│   │   ├── 🤖 peter_lynch.py        # 林奇实用投资代理
│   │   ├── 🤖 cathie_wood.py        # 伍德创新投资代理
│   │   ├── 🤖 bill_ackman.py        # 阿克曼激进投资代理
│   │   ├── 🤖 michael_burry.py      # 伯里逆势投资代理
│   │   ├── 🤖 aswath_damodaran.py   # 达摩达兰估值代理
│   │   ├── 🤖 stanley_druckenmiller.py # 德鲁肯米勒宏观代理
│   │   ├── 📊 fundamentals_analyst.py # 基本面分析师（支持LLM）
│   │   ├── 📊 market_analyst.py     # 市场分析师（支持LLM）
│   │   ├── 📊 news_analyst.py       # 新闻分析师（支持LLM）
│   │   ├── 📊 social_media_analyst.py # 社交媒体分析师（支持LLM）
│   │   ├── 📊 technicals.py         # 技术分析代理
│   │   ├── 📊 sentiment.py          # 情绪分析代理
│   │   ├── 📊 valuation.py          # 估值分析代理
│   │   ├── 🛡️ risk_manager.py       # 风险管理代理
│   │   ├── 💼 portfolio_manager.py  # 投资组合管理代理
│   │   └── 🔄 reflection_analyst.py # 反思分析代理
│   ├── 📂 config/                   # 配置管理
│   │   └── 📰 news_config.py        # 新闻数据配置
│   ├── 📂 data/                     # 数据处理
│   │   ├── 💾 cache.py              # 本地缓存系统
│   │   └── 📋 models.py             # 数据模型定义
│   ├── 📂 tools/                    # 工具模块
│   │   ├── 🔌 api.py                # API集成工具
│   │   └── 📰 local_news_reader.py  # 本地新闻读取器
│   ├── 📂 llm/                      # LLM集成
│   │   └── 🧠 models.py             # LLM模型定义
│   ├── 📂 utils/                    # 实用工具
│   │   ├── 👥 analysts.py           # 分析师配置
│   │   ├── 🎨 display.py            # 结果显示
│   │   └── 📊 visualize.py          # 数据可视化
│   ├── 🚀 main.py                   # 主程序入口
│   └── 📈 backtester.py             # 回测系统
├── 📂 app/                          # Web应用
│   ├── 📂 backend/                  # 后端API
│   │   ├── 📂 routes/               # API路由
│   │   ├── 📂 services/             # 业务服务
│   │   └── 🌐 main.py               # FastAPI应用
│   └── 📂 frontend/                 # 前端界面
│       ├── 📂 src/                  # React源代码
│       │   ├── 📂 components/       # React组件
│       │   └── 📂 services/         # 前端服务
│       └── 📦 package.json          # 前端依赖
├── 📂 docs/                         # 文档目录
│   ├── 📖 backtester_usage_guide.md # 回测使用指南
│   ├── 📖 groq_api_rotation_guide.md # GROQ API轮换指南
│   └── 📖 openrouter_integration_guide.md # OpenRouter集成指南
├── 📂 data/                         # 数据目录
│   ├── 📂 AAPL_alpha_news/          # AAPL Alpha Vantage新闻
│   ├── 📂 MSFT_alpha_news/          # MSFT Alpha Vantage新闻
│   ├── 📂 NVDA_alpha_news/          # NVDA Alpha Vantage新闻
│   └── 📂 social_media_data/        # 社交媒体数据
├── 📂 reasoning_logs/               # 推理日志
│   ├── 📂 experiment_YYYY-MM-DD/    # 实验日志
│   ├── 📂 accuracy_tracking/        # 准确性跟踪
│   └── 📂 reflections/              # 反思分析日志
├── 🐳 Dockerfile                    # Docker构建文件
├── 🐳 docker-compose.yml            # Docker编排文件
├── 📋 pyproject.toml                # Poetry项目配置
├── 🔧 .env.example                  # 环境变量示例
├── 📊 stocktwits_data_collector.py  # StockTwits数据收集器
└── 📖 README.md                     # 项目说明文档
```

### 核心文件说明

#### 🚀 主要入口点
- **`src/main.py`**: AI对冲基金主程序，处理命令行参数，创建代理工作流
- **`src/backtester.py`**: 回测系统入口，模拟历史交易策略表现
- **`app/backend/main.py`**: Web API后端服务
- **`app/frontend/src/App.tsx`**: React前端应用主组件

#### 🤖 代理系统
- **投资大师代理**: 模拟10位知名投资者的投资策略和决策风格
- **专业分析师**: 7个专业化分析代理，支持传统算法和LLM智能分析
- **管理代理**: 风险管理、投资组合管理、反思分析等核心功能

#### 📊 数据处理
- **`src/tools/api.py`**: 金融数据API集成，支持多种数据源
- **`src/tools/local_news_reader.py`**: 本地新闻数据读取器
- **`src/data/cache.py`**: 本地缓存系统，减少API调用
- **`src/config/news_config.py`**: 新闻数据配置管理

#### 🧠 LLM集成
- **`src/llm/models.py`**: LLM模型定义和工具
- **`src/utils/llm.py`**: LLM调用工具和提示工程
- **多提供商支持**: OpenAI、Groq、Anthropic、DeepSeek等

#### 📈 回测和分析
- **推理日志系统**: 详细记录AI代理的决策过程
- **准确性跟踪**: 监控分析师信号的准确性
- **反思学习**: 持续改进决策质量的学习机制
- **性能评估**: 全面的回测性能分析和可视化

## 🛠️ 故障排除

### 常见问题及解决方案

#### 🔑 API密钥相关问题

**问题1：API密钥错误或无效**
```bash
# 检查环境变量
echo $OPENAI_API_KEY
echo $GROQ_API_KEY1

# 验证.env文件
cat .env | grep API_KEY
```

**解决方案**：
- 确保API密钥格式正确
- 检查密钥是否已过期
- 验证密钥权限和配额

**问题2：GROQ API速率限制**
```
Error: Rate limit exceeded (429)
```

**解决方案**：
- 配置多个GROQ API密钥（GROQ_API_KEY1-4）
- 增加请求间隔时间
- 使用其他LLM提供商

#### 📰 本地新闻数据问题

**问题3：找不到本地新闻数据文件**
```
⚠️ Alpha Vantage数据文件不存在: AAPL_alpha_news/alpha_news_2024-01-01.json
```

**解决方案**：
```python
# 检查数据可用性
from src.config.news_config import news_config
availability = news_config.check_local_data_availability()
print(availability)

# 验证目录结构
import os
for ticker in ['AAPL', 'MSFT', 'NVDA']:
    for source in ['alpha_news', 'news_api_news', 'finnhub_news']:
        dir_path = f"{ticker}_{source}"
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}: {len(os.listdir(dir_path))} 文件")
        else:
            print(f"❌ {dir_path}: 目录不存在")
```

**问题4：新闻配置导入失败**
```
⚠️ 无法导入本地新闻配置，使用API模式
```

**解决方案**：
- 检查配置文件格式
- 验证环境变量设置
- 使用默认配置

#### 🧠 LLM模型问题

**问题5：模型响应超时或错误**
```
Error: Request timeout / Model not available
```

**解决方案**：
```python
# 测试不同模型
python src/backtester.py --model gpt-4o-mini --provider OpenAI
python src/backtester.py --model deepseek-v3 --provider Groq
python src/backtester.py --ollama  # 使用本地模型
```

**问题6：内存不足**
```
MemoryError: Unable to allocate memory
```

**解决方案**：
- 减少回测时间范围
- 降低新闻数量限制
- 使用更少的股票代码
- 启用本地数据模式

#### 🐳 Docker相关问题

**问题7：Docker构建失败**
```bash
# 清理Docker缓存
docker system prune -a

# 重新构建
./run.sh build
```

**问题8：容器启动失败**
```bash
# 检查日志
docker logs ai-hedge-fund

# 检查端口占用
netstat -tulpn | grep :8000
```

### 调试工具和命令

#### 配置检查
```python
# 检查新闻配置状态
from src.config.news_config import print_news_config_status
print_news_config_status()

# 测试数据读取
from src.tools.local_news_reader import get_local_multi_source_news
news = get_local_multi_source_news(ticker="AAPL", limit=1)
print(f"测试结果: {len(news)} 条新闻")
```

#### 性能监控
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG

# 监控API调用
python src/backtester.py --tickers AAPL --show-reasoning --save-reasoning
```

#### 数据验证
```python
# 验证本地数据完整性
import json
import os

def validate_news_data(ticker, date):
    """验证特定日期的新闻数据"""
    sources = ['alpha_news', 'news_api_news', 'finnhub_news']
    for source in sources:
        file_path = f"{ticker}_{source}/{source.replace('_news', '')}_{date}.json"
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"✅ {source}: {len(data)} 条新闻")
            except Exception as e:
                print(f"❌ {source}: 数据格式错误 - {e}")
        else:
            print(f"⚠️ {source}: 文件不存在")

# 使用示例
validate_news_data("AAPL", "2024-01-15")
```

### 性能优化建议

#### 提高回测速度
1. **使用本地数据**：避免API调用延迟
2. **选择快速模型**：deepseek-v3, llama-4-scout-17b
3. **并行处理**：减少分析师数量，使用多线程
4. **缓存优化**：启用数据缓存机制

#### 降低成本
1. **免费模型**：使用OpenRouter免费模型
2. **本地模型**：使用Ollama本地推理
3. **API轮换**：配置多个API密钥
4. **智能调用**：只在必要时使用高质量模型

#### 提高准确性
1. **高质量模型**：gpt-4o, claude-3-sonnet
2. **多源数据**：结合多个新闻源
3. **推理日志**：启用详细推理记录
4. **准确性跟踪**：监控分析师表现

## 代理提示词

本节详细介绍了每个代理使用的提示词模板，这些模板指导代理如何分析数据并做出投资决策。提示词是代理"个性"和投资风格的核心，决定了代理如何解释数据并形成观点。

### 投资风格代理提示词

#### Warren Buffett 代理提示词

**System 提示词**：
```
You are a Warren Buffett AI agent. Decide on investment signals based on Warren Buffett's principles:
- Circle of Competence: Only invest in businesses you understand
- Margin of Safety (> 30%): Buy at a significant discount to intrinsic value
- Economic Moat: Look for durable competitive advantages
- Quality Management: Seek conservative, shareholder-oriented teams
- Financial Strength: Favor low debt, strong returns on equity
- Long-term Horizon: Invest in businesses, not just stocks
...
```

*中文翻译：你是一个沃伦·巴菲特AI代理。基于巴菲特的原则做出投资信号：能力圈（只投资你理解的企业）、安全边际（>30%，以显著低于内在价值的价格购买）、经济护城河（寻找持久的竞争优势）、优质管理层（寻找保守的、以股东为导向的团队）、财务实力（偏好低债务、高股本回报率）、长期视角（投资企业，而非仅仅是股票）...*

**Human 提示词**：
```
Based on the following data, create the investment signal as Warren Buffett would:

Analysis Data for {ticker}:
{analysis_data}

Return the trading signal in the following JSON format exactly:
{
  "signal": "bullish" | "bearish" | "neutral",
  "confidence": float between 0 and 100,
  "reasoning": "string"
}
```

*中文翻译：基于以下数据，创建沃伦·巴菲特风格的投资信号：分析数据为{ticker}：{analysis_data}。请以以下JSON格式精确返回交易信号：{"signal": "bullish" | "bearish" | "neutral", "confidence": 0到100之间的浮点数, "reasoning": "字符串"}*

#### Charlie Munger 代理提示词

**System 提示词**：
```
You are a Charlie Munger AI agent, making investment decisions using his principles:
...
Rules:
- Praise businesses with predictable, consistent operations and cash flows.
- Value businesses with high ROIC and pricing power.
- Prefer simple businesses with understandable economics.
- Admire management with skin in the game and shareholder-friendly capital allocation.
- Focus on long-term economics rather than short-term metrics.
- Be skeptical of businesses with rapidly changing dynamics or excessive share dilution.
- Avoid excessive leverage or financial engineering.
- Provide a rational, data-driven recommendation (bullish, bearish, or neutral).
...
```

*中文翻译：你是一个查理·芒格AI代理，使用他的原则做出投资决策：...规则：赞赏业务可预测、稳定的运营和现金流的企业；重视具有高投资回报率和定价能力的企业；偏好经济学简单易懂的企业；欣赏有利益相关和对股东友好的资本配置的管理层；关注长期经济效益而非短期指标；对业务快速变化或过度股份稀释的企业持怀疑态度；避免过度杠杆或金融工程；提供理性、数据驱动的建议（看涨、看跌或中性）...*

#### Ben Graham 代理提示词

**System 提示词**：
```
You are a Benjamin Graham AI agent, making investment decisions using his principles:
1. Insist on a margin of safety by buying below intrinsic value (e.g., using Graham Number, net-net).
2. Emphasize the company's financial strength (low leverage, ample current assets).
3. Prefer stable earnings over multiple years.
4. Consider dividend record for extra safety.
5. Avoid speculative or high-growth assumptions; focus on proven metrics.
...
```

*中文翻译：你是一个本杰明·格雷厄姆AI代理，使用他的原则做出投资决策：1. 坚持安全边际，以低于内在价值的价格购买（例如，使用格雷厄姆数字、净净值）；2. 强调公司的财务实力（低杠杆、充足的流动资产）；3. 偏好多年稳定的收益；4. 考虑股息记录以增加安全性；5. 避免投机或高增长假设；专注于已证实的指标...*

#### Phil Fisher 代理提示词

**System 提示词**：
```
You are a Phil Fisher AI agent, making investment decisions using his principles:

1. Emphasize long-term growth potential and quality of management.
2. Focus on companies investing in R&D for future products/services.
3. Look for strong profitability and consistent margins.
4. Willing to pay more for exceptional companies but still mindful of valuation.
5. Rely on thorough research (scuttlebutt) and thorough fundamental checks.
...
```

*中文翻译：你是一个菲利普·费舍尔AI代理，使用他的原则做出投资决策：1. 强调长期增长潜力和管理质量；2. 关注投资研发以开发未来产品/服务的公司；3. 寻找强大的盈利能力和稳定的利润率；4. 愿意为卓越公司支付更高价格，但仍注意估值；5. 依靠彻底的研究（小道消息）和全面的基本面检查...*

#### Peter Lynch 代理提示词

**System 提示词**：
```
You are a Peter Lynch AI agent. You make investment decisions based on Peter Lynch's well-known principles:
...
When you provide your reasoning, do it in Peter Lynch's voice:
- Cite the PEG ratio
- Mention 'ten-bagger' potential if applicable
- Refer to personal or anecdotal observations (e.g., "If my kids love the product...")
- Use practical, folksy language
- Provide key positives and negatives
- Conclude with a clear stance (bullish, bearish, or neutral)
```

*中文翻译：你是一个彼得·林奇AI代理。你基于彼得·林奇著名的原则做出投资决策：...当你提供推理时，使用彼得·林奇的语气：引用PEG比率；如果适用，提及"十倍股"潜力；引用个人或轶事观察（例如，"如果我的孩子喜欢这个产品..."）；使用实用、通俗的语言；提供关键的正面和负面因素；以明确的立场结束（看涨、看跌或中性）*

#### Cathie Wood 代理提示词

**System 提示词**：
```
You are a Cathie Wood AI agent, making investment decisions using her principles:

1. Seek companies leveraging disruptive innovation.
2. Emphasize exponential growth potential, large TAM.
3. Focus on technology, healthcare, or other future-facing sectors.
4. Consider multi-year time horizons for potential breakthroughs.
5. Accept higher volatility in pursuit of high returns.
6. Evaluate management's vision and ability to invest in R&D.
...
```

*中文翻译：你是一个凯茜·伍德AI代理，使用她的原则做出投资决策：1. 寻找利用颠覆性创新的公司；2. 强调指数级增长潜力，大型总可寻址市场；3. 专注于技术、医疗保健或其他面向未来的行业；4. 考虑多年时间范围内的潜在突破；5. 接受更高的波动性以追求高回报；6. 评估管理层的愿景和投资研发的能力...*

#### Bill Ackman 代理提示词

**System 提示词**：
```
You are a Bill Ackman AI agent, making investment decisions using his principles:

1. Seek high-quality businesses with durable competitive advantages (moats), often in well-known consumer or service brands.
2. Prioritize consistent free cash flow and growth potential over the long term.
3. Advocate for strong financial discipline (reasonable leverage, efficient capital allocation).
4. Valuation matters: target intrinsic value with a margin of safety.
5. Consider activism where management or operational improvements can unlock substantial upside.
6. Concentrate on a few high-conviction investments.
...
```

*中文翻译：你是一个比尔·阿克曼AI代理，使用他的原则做出投资决策：1. 寻找具有持久竞争优势（护城河）的高质量企业，通常是知名消费品或服务品牌；2. 优先考虑长期稳定的自由现金流和增长潜力；3. 倡导强大的财务纪律（合理的杠杆，高效的资本配置）；4. 估值很重要：以安全边际为目标的内在价值；5. 考虑激进主义，通过管理或运营改进释放巨大上升空间；6. 集中于少数高确信度投资...*

#### Michael Burry 代理提示词

**System 提示词**：
```
You are an AI agent emulating Dr. Michael J. Burry. Your mandate:
- Hunt for deep value in US equities using hard numbers (free cash flow, EV/EBIT, balance sheet)
- Be contrarian: hatred in the press can be your friend if fundamentals are solid
- Focus on downside first – avoid leveraged balance sheets
- Look for hard catalysts such as insider buying, buybacks, or asset sales
- Communicate in Burry's terse, data‑driven style
...
```

*中文翻译：你是一个模拟迈克尔·J·伯里博士的AI代理。你的任务是：使用硬数据（自由现金流、企业价值/息税前利润、资产负债表）在美国股票中寻找深度价值；保持逆向思维：如果基本面坚实，媒体的负面评价可能是你的朋友；首先关注下行风险 - 避免高杠杆资产负债表；寻找明确的催化剂，如内部人士购买、股票回购或资产出售；以伯里简洁、数据驱动的风格沟通...*

#### Aswath Damodaran 代理提示词

**System 提示词**：
```
You are Aswath Damodaran, Professor of Finance at NYU Stern.
Use your valuation framework to issue trading signals on US equities.

Speak with your usual clear, data‑driven tone:
  ◦ Start with the company "story" (qualitatively)
  ◦ Connect that story to key numerical drivers: revenue growth, margins, reinvestment, risk
  ◦ Conclude with value: your FCFF DCF estimate, margin of safety, and relative valuation sanity checks
  ◦ Highlight major uncertainties and how they affect value
Return ONLY the JSON specified below.
```

*中文翻译：你是纽约大学斯特恩商学院的金融学教授阿斯瓦斯·达摩达兰。使用你的估值框架对美国股票发出交易信号。以你惯常的清晰、数据驱动的语调发言：首先介绍公司的"故事"（定性）；将该故事与关键数字驱动因素联系起来：收入增长、利润率、再投资、风险；以价值结束：你的FCFF DCF估计、安全边际和相对估值合理性检查；强调主要不确定性及其如何影响价值。仅返回下面指定的JSON。*

#### Stanley Druckenmiller 代理提示词

**System 提示词**：
```
You are a Stanley Druckenmiller AI agent, making investment decisions using his principles:

1. Seek asymmetric risk-reward opportunities (large upside, limited downside).
2. Emphasize growth, momentum, and market sentiment.
3. Preserve capital by avoiding major drawdowns.
4. Willing to pay higher valuations for true growth leaders.
5. Be aggressive when conviction is high.
6. Cut losses quickly if the thesis changes.
...
```

*中文翻译：你是一个斯坦利·德鲁肯米勒AI代理，使用他的原则做出投资决策：1. 寻找不对称风险回报机会（大幅上涨潜力，有限下行风险）；2. 强调增长、动量和市场情绪；3. 通过避免重大回撤来保全资本；4. 愿意为真正的增长领导者支付更高估值；5. 在确信度高时积极进取；6. 如果投资论点改变，迅速止损...*

### 分析代理提示词

分析代理（基本面、技术面、情绪和估值分析代理）主要使用算法和定量分析，而非LLM提示词。这些代理通过直接计算和分析财务指标、价格数据、情绪信号和估值模型来生成交易信号，不依赖于特定的提示词模板。

### 管理代理提示词

#### 投资组合管理代理提示词

投资组合管理代理使用LLM来综合所有分析师信号并做出最终决策。其提示词指导LLM如何权衡不同信号、考虑风险限制，并生成最终交易决策。由于代码中未直接展示其完整提示词，但其核心功能是整合所有代理信号，应用风险限制，并生成最终交易决策（买入、卖出、做空、平仓或持有）。

## 项目结构

```
ai-hedge-fund/
├── src/                          # 源代码目录
│   ├── agents/                   # 代理定义和工作流
│   │   ├── aswath_damodaran.py   # Aswath Damodaran代理 - 估值专家
│   │   ├── ben_graham.py         # Ben Graham代理 - 价值投资之父
│   │   ├── bill_ackman.py        # Bill Ackman代理 - 激进投资者
│   │   ├── cathie_wood.py        # Cathie Wood代理 - 创新投资专家
│   │   ├── charlie_munger.py     # Charlie Munger代理 - 巴菲特合伙人
│   │   ├── fundamentals.py       # 基本面分析代理
│   │   ├── michael_burry.py      # Michael Burry代理 - 逆势投资者
│   │   ├── peter_lynch.py        # Peter Lynch代理 - 实用投资者
│   │   ├── phil_fisher.py        # Phil Fisher代理 - 成长型投资者
│   │   ├── portfolio_manager.py  # 投资组合管理代理 - 最终决策者
│   │   ├── reflection_analyst.py # 反思分析代理 - 决策质量分析
│   │   ├── risk_manager.py       # 风险管理代理 - 风险评估
│   │   ├── sentiment.py          # 情绪分析代理 - 市场情绪分析
│   │   ├── stanley_druckenmiller.py # Stanley Druckenmiller代理 - 宏观投资者
│   │   ├── technicals.py         # 技术分析代理 - 技术指标分析
│   │   ├── valuation.py          # 估值分析代理 - 内在价值计算
│   │   ├── warren_buffett.py     # Warren Buffett代理 - 价值投资者
│   ├── data/                     # 数据处理模块
│   │   ├── cache.py              # 本地缓存系统 - 减少API调用
│   │   ├── models.py             # 数据模型定义 - Pydantic模型
│   ├── graph/                    # 工作流图模块
│   │   ├── state.py              # 状态管理 - 代理状态和工作流状态
│   ├── llm/                      # LLM集成模块
│   │   ├── api_models.json       # API模型配置
│   │   ├── models.py             # LLM模型定义和工具
│   │   ├── ollama_models.json    # Ollama本地模型配置
│   ├── tools/                    # 代理工具
│   │   ├── api.py                # API工具 - 金融数据获取
│   ├── utils/                    # 实用工具
│   │   ├── analysts.py           # 分析师工具 - 代理配置
│   │   ├── display.py            # 显示工具 - 结果格式化
│   │   ├── docker.py             # Docker工具 - 容器支持
│   │   ├── llm.py                # LLM工具 - 模型调用
│   │   ├── ollama.py             # Ollama工具 - 本地LLM支持
│   │   ├── progress.py           # 进度工具 - 进度跟踪
│   │   ├── visualize.py          # 可视化工具 - 图表生成
│   ├── backtester.py             # 回测工具 - 策略回测
│   ├── main.py                   # 主入口点 - 程序启动
├── app/                          # Web应用目录
│   ├── backend/                  # 后端API
│   │   ├── models/               # 后端数据模型
│   │   │   ├── events.py         # 事件模型 - SSE事件
│   │   │   ├── schemas.py        # 模式定义 - API请求/响应
│   │   ├── routes/               # API路由
│   │   │   ├── hedge_fund.py     # 对冲基金路由 - 主API端点
│   │   │   ├── health.py         # 健康检查路由
│   │   ├── services/             # 后端服务
│   │   │   ├── graph.py          # 图服务 - 工作流管理
│   │   │   ├── portfolio.py      # 投资组合服务 - 投资组合管理
│   │   ├── main.py               # 后端入口点 - FastAPI应用
│   ├── frontend/                 # 前端应用
│   │   ├── src/                  # 前端源代码
│   │   │   ├── components/       # React组件
│   │   │   │   ├── Flow.tsx      # 流程组件 - 代理流程图
│   │   │   │   ├── Layout.tsx    # 布局组件 - 页面布局
│   │   │   │   ├── sidebar/      # 侧边栏组件
│   │   │   │   ├── ui/           # UI组件库
│   │   │   ├── contexts/         # React上下文
│   │   │   ├── data/             # 前端数据
│   │   │   │   ├── agents.ts     # 代理定义
│   │   │   │   ├── models.ts     # 模型定义
│   │   │   ├── nodes/            # 流程图节点
│   │   │   ├── services/         # 前端服务
│   │   │   │   ├── api.ts        # API服务 - 后端通信
│   │   │   ├── App.tsx           # 主应用组件
│   │   │   ├── main.tsx          # 前端入口点
├── docker-compose.yml            # Docker Compose配置
├── Dockerfile                    # Docker构建文件
├── pyproject.toml                # Poetry项目配置
├── run.sh                        # Linux/Mac运行脚本
├── run.bat                       # Windows运行脚本
├── .env.example                  # 环境变量示例
```

### 核心文件功能说明

#### 主要入口点
- **src/main.py**: 对冲基金的主入口点，处理命令行参数，创建代理工作流，并执行交易决策。
- **src/backtester.py**: 回测系统的入口点，模拟在历史数据上执行交易策略。

#### 代理系统
- **src/agents/warren_buffett.py**: 实现了Warren Buffett的投资策略，分析公司基本面、经济护城河、管理质量，并计算内在价值。
- **src/agents/portfolio_manager.py**: 最终决策代理，综合所有分析师信号、风险评估和前一日反思建议，生成交易决策。
- **src/agents/reflection_analyst.py**: 反思分析代理，分析投资决策质量，提供改进建议，形成持续学习机制。
- **src/agents/risk_manager.py**: 风险管理代理，评估投资组合风险并设置仓位限制。

#### 数据处理
- **src/tools/api.py**: 提供与金融数据API的集成，获取股票价格、财务指标、内部交易和公司新闻。
- **src/data/cache.py**: 实现本地缓存系统，减少API调用并提高性能。
- **src/data/models.py**: 定义数据模型，使用Pydantic进行数据验证和序列化。

#### LLM集成
- **src/llm/models.py**: 定义LLM模型接口，支持多种LLM提供商。
- **src/utils/llm.py**: 提供LLM调用工具，处理提示工程和响应解析。
- **src/utils/ollama.py**: 支持本地LLM推理，通过Ollama集成开源模型。

#### 工作流引擎
- **src/graph/state.py**: 管理代理状态和工作流状态，实现基于LangGraph的状态图。
- **src/utils/analysts.py**: 配置分析师代理，定义代理顺序和工作流连接。

#### Web应用
- **app/backend/main.py**: FastAPI后端应用的入口点，提供API服务。
- **app/backend/routes/hedge_fund.py**: 对冲基金API路由，处理前端请求并返回结果。
- **app/frontend/src/App.tsx**: React前端应用的主组件，提供用户界面。
- **app/frontend/src/components/Flow.tsx**: 流程图组件，可视化代理工作流和决策过程。

## 贡献指南

1. Fork仓库
2. 创建功能分支
3. 提交您的更改
4. 推送到分支
5. 创建Pull Request

**重要提示**：请保持您的pull requests小而集中。这将使审查和合并更容易。

## 功能请求

如果您有功能请求，请打开一个[issue](https://github.com/virattt/ai-hedge-fund/issues)，并确保它被标记为`enhancement`。

## 许可证

本项目采用MIT许可证 - 详情请参阅LICENSE文件。
